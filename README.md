# Scholara Student App - Development Guidelines

Welcome to the Scholara Student App development repository. This document outlines the foundational rules, structure, tools, and development practices to be followed throughout the project to ensure clean, scalable, and production-ready code.

---

## Development Philosophy

- Follow a modular, **feature-wise approach** with a clear separation of concerns.
- **Do not rush** development. Break complex features into smaller parts and use `TODO:` markers when needed.
- Code should be **error-free** — resolve all runtime and info-level issues before moving forward.
- Maintain **high readability** — avoid overly long files or deeply nested logic.
- Add meaningful **comments** to clarify complex or non-obvious logic.

---

## Development Workflow

We follow a clear, repeatable process for building features and modules in Scholara:

### 1. Feature Planning
- Define feature scope: screens, widgets, logic, end goals
- Structure folders under `features/{feature_name}`

### 2. Models & Enums
- Create all necessary models and enums
- Generate mock data if needed (using `faker` or manually)

### 3. UI/UX Implementation
- Generate modular and responsive widgets based on the prompts
- Follow the global theme strictly
- add responsive UI using `flutter_screenutil` wherever seems appropriate, don't need to add it everywhere.

### 4. State Management & Backend
- Implement business logic using Riverpod
- Connect Firestore/Auth/Storage where required
- Use `shared_preferences` and `flutter_secure_storage` appropriately
- Include validation and error handling

### 5. Logging & Analytics
- Add logs using the `logger` package
- Track screen views, errors, and user actions
- Ensure maximum observability where applicable

### 6. Testing
- Write unit tests, widget tests, and integration tests for each module
- Ask ChatGPT for guidance if unfamiliar with a test type

### 7. Final Cleanup
- Resolve all errors and warnings
- Remove mocks, test data, and unused files
- Ensure full adherence to project standards

---

## Project Structure

### Core Folder Structure (`lib/core/`)

| Folder | Purpose |
|--------|---------|
| `constants/` | App-wide constants like colors, text styles, API endpoints |
| `theme/` | Light/dark theme setup using Flutter's ThemeData |
| `routes/` | `go_router` setup and centralized route definitions |
| `widgets/` | Global reusable widgets |
| `models/` | Shared models used across multiple features |
| `enums/` | Shared enums (e.g., `UserRole`) |
| `providers/` | Global Riverpod providers (e.g., auth, preferences) |
| `services/` | Global services (auth, API, storage, etc.) |
| `utils/` | Helpers, formatters, and extensions |

### Feature Structure (`lib/features/{feature_name}/`)

Each feature should have its own folder with:
- `screens/`
- `controllers/`
- `repositories/`
- `widgets/`
- `models/` (if needed)

If any folder (especially `widgets/`) becomes too large, break it down into subfolders for organization.

---

## API & Data Management

- Use `dio` for API calls.
- Centralize API endpoints in `lib/core/constants/api_endpoints.dart`.
- Handle API errors and edge cases within repositories.
- Use `shared_preferences` for local data storage.
- Use `flutter_secure_storage` for sensitive data storage.

---

## Widget Development Rules

- Prefer creating separate **Stateless/StatefulWidgets** instead of UI methods inside other widgets.
- Use **global widgets** (from `core/widgets/`) where reuse is expected.
- Use **local widgets** (within feature folders) for feature-specific elements.
- Avoid deeply nested layouts — split long UIs into smaller components.
- Add **comments** wherever helpful, especially for business logic or conditional flows.

---

## Theme & Styling

- Use Flutter's `ThemeData` system to manage light and dark themes.
- Global styling should come from:
  - `AppColors`
  - `AppTypography`
- Do **not** hardcode `TextStyle` or color values.
- Layout spacing can be added inline as needed — no `LayoutGuidelines` file will be used.

---

## Libraries & Tools

### Preferred Packages

| Purpose | Package |
|--------|---------|
| State Management | `riverpod` |
| State Management | `flutter_riverpod` |
| Routing | `go_router` |
| Responsive UI | `flutter_screenutil` |
| Mock Data | `faker` |
| Secure Storage | `flutter_secure_storage` |
| Persistent Settings | `shared_preferences` |
| Fonts | `google_fonts` |
| Logging | `logger` |
| Icons | `material_symbols_icons` |

### Avoided Tools

- Code generators like `freezed`, `json_serializable`, or `build_runner`
- Unstable or under-documented packages

---

## Adaptive UI

- Currently we are only developing for mobile but we need to keep scope for other devices
- don't confuse adaptive UI with responsive UI.
- Instead of using raw `LayoutBuilder` everywhere, we create widgets like `ResponsivePage`
- Use `Expanded` and `Flexible` widgets judiciously.
- Avoid nested `SizedBox` for layout purposes.

---

## Functional Scope (MVP)

- **Localization**: Not included in the current phase.
- **Offline-first**: Deferred to a later stage.
- **Analytics & Logging**: Must be added as early as possible. Include:
  - Error logging with stack traces
  - Page and event tracking
  - Detailed logs using `logger` package
  - as we are using firebase we can also use its products like analytics and crashlytics.

---

## Testing

After each major module is complete, testing must be implemented:
- **Unit tests** for core logic and controllers
- **Widget tests** for key screens and layouts
- **Integration tests** when applicable

If unsure about how to structure or write tests, ask for guidance in the ChatGPT planning thread.

---

## Cache

- Use proper methods of caching
- Use packeges like catched_network_image along with proper placeholders and error images.

---

## Final Reminders

- Use centralized styles and configurations at all times.
- Do not hardcode values (colors, sizes, routes).
- Use `AppRoutes` and `RouteNames` for navigation.
- Add `TODO:` comments and split into parts if a screen or feature grows too complex.
- Maintain **consistent, clean, and scalable** code throughout the project.

---

**Let’s build this the right way — one clean feature at a time.**
