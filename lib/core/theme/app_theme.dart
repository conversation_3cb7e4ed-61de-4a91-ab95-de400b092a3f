import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';

class AppTheme {
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    useMaterial3: true,
    scaffoldBackgroundColor: AppColors.backgroundLight,
    primaryColor: AppColors.primaryLight,
    colorScheme: const ColorScheme.light(
      primary: AppColors.primaryLight,
      secondary: AppColors.secondaryLight,
      surface: AppColors.surfaceLight,
      error: AppColors.errorLight,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: AppColors.textPrimaryLight,
      onError: Colors.white,
    ),
    dividerColor: AppColors.dividerLight,
    disabledColor: AppColors.buttonDisabledLight,
    textTheme: AppTypography.getTextTheme(Brightness.light),
    appBarTheme: const AppBarTheme(
      elevation: 0,
      backgroundColor: AppColors.surfaceLight,
      foregroundColor: AppColors.textPrimaryLight,
    ),
    cardColor: AppColors.surfaceLight,
    shadowColor: AppColors.shadowLowLight,
    tooltipTheme: TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColors.tooltipLight,
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(color: Colors.white),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,
    scaffoldBackgroundColor: AppColors.backgroundDark,
    primaryColor: AppColors.primaryDark,
    colorScheme: const ColorScheme.dark(
      primary: AppColors.primaryDark,
      secondary: AppColors.secondaryDark,
      surface: AppColors.surfaceDark,
      error: AppColors.errorDark,
      onPrimary: Colors.black,
      onSecondary: Colors.black,
      onSurface: AppColors.textPrimaryDark,
      onError: Colors.black,
    ),
    dividerColor: AppColors.dividerDark,
    disabledColor: AppColors.buttonDisabledDark,
    textTheme: AppTypography.getTextTheme(Brightness.dark),
    appBarTheme: const AppBarTheme(
      elevation: 0,
      backgroundColor: AppColors.surfaceDark,
      foregroundColor: AppColors.textPrimaryDark,
    ),
    cardColor: AppColors.surfaceDark,
    shadowColor: AppColors.shadowLowDark,
    tooltipTheme: TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColors.tooltipDark,
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(color: Colors.black),
    ),
  );
}
