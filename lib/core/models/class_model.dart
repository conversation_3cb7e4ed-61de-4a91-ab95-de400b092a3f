/// Basic model representing a class in the Scholara student app
/// This is a minimal implementation for future class management features
class ClassModel {
  /// Unique identifier for the class
  final String id;
  
  /// Name of the class (e.g., "Mathematics 10A", "Physics 12B")
  final String name;
  
  /// Subject of the class (e.g., "Mathematics", "Physics")
  final String subject;
  
  /// ID of the teacher who manages this class
  final String teacherId;
  
  /// Name of the teacher (for display purposes)
  final String? teacherName;
  
  /// List of student user IDs enrolled in this class
  final List<String> studentIds;
  
  /// When the class was created
  final DateTime? createdAt;
  
  /// Whether the class is currently active
  final bool isActive;

  const ClassModel({
    required this.id,
    required this.name,
    required this.subject,
    required this.teacherId,
    this.teacherName,
    required this.studentIds,
    this.createdAt,
    this.isActive = true,
  });

  /// Create a ClassModel from JSON
  factory ClassModel.fromJson(Map<String, dynamic> json) {
    return ClassModel(
      id: json['id'] as String,
      name: json['name'] as String,
      subject: json['subject'] as String,
      teacherId: json['teacherId'] as String,
      teacherName: json['teacherName'] as String?,
      studentIds: List<String>.from(json['studentIds'] as List),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// Convert ClassModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'studentIds': studentIds,
      'createdAt': createdAt?.toIso8601String(),
      'isActive': isActive,
    };
  }

  /// Create a copy of this ClassModel with updated fields
  ClassModel copyWith({
    String? id,
    String? name,
    String? subject,
    String? teacherId,
    String? teacherName,
    List<String>? studentIds,
    DateTime? createdAt,
    bool? isActive,
  }) {
    return ClassModel(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      studentIds: studentIds ?? this.studentIds,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClassModel &&
        other.id == id &&
        other.name == name &&
        other.subject == subject &&
        other.teacherId == teacherId &&
        other.teacherName == teacherName &&
        _listEquals(other.studentIds, studentIds) &&
        other.createdAt == createdAt &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      subject,
      teacherId,
      teacherName,
      Object.hashAll(studentIds),
      createdAt,
      isActive,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'ClassModel(id: $id, name: $name, subject: $subject, studentCount: ${studentIds.length})';
  }
}
