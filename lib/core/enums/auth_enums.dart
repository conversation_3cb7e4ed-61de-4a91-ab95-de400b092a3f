/// Enum representing the current authentication state
enum AuthState { unauthenticated, authenticated, loading, error }

/// Enum representing different types of authentication errors
enum AuthErrorType {
  invalidEmail,
  weakPassword,
  emailAlreadyInUse,
  userNotFound,
  wrongPassword,
  userDisabled,
  tooManyRequests,
  networkError,
  unknown,
  operationNotAllowed,
  invalidCredential,
  accountExistsWithDifferentCredential,
  requiresR<PERSON>entLogin,
  providerAlreadyLinked,
  noSuchProvider,
  invalidUserToken,
  userTokenExpired,
  appNotAuthorized,
  expiredActionCode,
  invalidActionCode,
  invalidMessagePayload,
  invalidSender,
  invalidRecipientEmail,
  missingAndroidPackageName,
  missingContinueUri,
  missingIosBundleId,
  invalidContinueU<PERSON>,
  unauthorizedContinueUri,
}

/// Extension to get user-friendly error messages
extension AuthErrorTypeExtension on AuthErrorType {
  /// Get a user-friendly error message for the auth error type
  String get message {
    switch (this) {
      case AuthErrorType.invalidEmail:
        return 'Please enter a valid email address.';
      case AuthErrorType.weakPassword:
        return 'Password should be at least 6 characters long.';
      case AuthErrorType.emailAlreadyInUse:
        return 'An account already exists with this email address.';
      case AuthErrorType.userNotFound:
        return 'No account found with this email address.';
      case AuthErrorType.wrongPassword:
        return 'Incorrect password. Please try again.';
      case AuthErrorType.userDisabled:
        return 'This account has been disabled. Please contact support.';
      case AuthErrorType.tooManyRequests:
        return 'Too many failed attempts. Please try again later.';
      case AuthErrorType.networkError:
        return 'Network error. Please check your connection and try again.';
      case AuthErrorType.operationNotAllowed:
        return 'This operation is not allowed. Please contact support.';
      case AuthErrorType.invalidCredential:
        return 'Invalid credentials. Please check your email and password.';
      case AuthErrorType.accountExistsWithDifferentCredential:
        return 'An account already exists with a different sign-in method.';
      case AuthErrorType.requiresRecentLogin:
        return 'Please sign in again to complete this action.';
      case AuthErrorType.providerAlreadyLinked:
        return 'This account is already linked to another provider.';
      case AuthErrorType.noSuchProvider:
        return 'No such provider is linked to this account.';
      case AuthErrorType.invalidUserToken:
        return 'Your session has expired. Please sign in again.';
      case AuthErrorType.userTokenExpired:
        return 'Your session has expired. Please sign in again.';
      case AuthErrorType.appNotAuthorized:
        return 'This app is not authorized to use Firebase Authentication.';
      case AuthErrorType.expiredActionCode:
        return 'This action code has expired.';
      case AuthErrorType.invalidActionCode:
        return 'This action code is invalid.';
      case AuthErrorType.invalidMessagePayload:
        return 'Invalid message payload.';
      case AuthErrorType.invalidSender:
        return 'Invalid sender.';
      case AuthErrorType.invalidRecipientEmail:
        return 'Invalid recipient email.';
      case AuthErrorType.missingAndroidPackageName:
        return 'Missing Android package name.';
      case AuthErrorType.missingContinueUri:
        return 'Missing continue URI.';
      case AuthErrorType.missingIosBundleId:
        return 'Missing iOS bundle ID.';
      case AuthErrorType.invalidContinueUri:
        return 'Invalid continue URI.';
      case AuthErrorType.unauthorizedContinueUri:
        return 'Unauthorized continue URI.';
      case AuthErrorType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/// Enum representing different authentication operations
enum AuthOperation {
  /// Sign in operation
  signIn,

  /// Sign up operation
  signUp,

  /// Sign out operation
  signOut,

  /// Password reset operation
  resetPassword,

  /// Email verification operation
  verifyEmail,

  /// Update profile operation
  updateProfile,

  /// Update password operation
  updatePassword,

  /// Delete account operation
  deleteAccount,

  /// Refresh token operation
  refreshToken,
}
