import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'mock_homeworks.dart'; // Make sure this has your mockHomeworkList and mockSubmissions

Future<void> uploadMockHomeworkData() async {
  final firestore = FirebaseFirestore.instance;

  // Upload each homework item
  for (final homework in mockHomeworkList) {
    final homeworkDoc = firestore.collection('homework').doc(homework.id);
    await homeworkDoc.set(homework.toJson());
    debugPrint('Uploaded homework: ${homework.id}');
  }

  // Upload submissions if available
  for (final submission in mockHomeworkSubmissions) {
    final submissionDoc = firestore
        .collection('homework_submissions')
        .doc('${submission.homeworkId}_${submission.userId}');
    await submissionDoc.set(submission.toJson());
    debugPrint('Uploaded submission: ${submission.homeworkId}_${submission.userId}');
  }
}
