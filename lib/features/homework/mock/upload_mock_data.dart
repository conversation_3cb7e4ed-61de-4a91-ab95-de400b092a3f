import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'mock_classes.dart';
import 'mock_homeworks.dart';

Future<void> uploadMockHomeworkData() async {
  final firestore = FirebaseFirestore.instance;

  try {
    debugPrint('Starting mock data upload...');

    // Upload class data first
    debugPrint('Uploading ${mockClassesList.length} classes...');
    for (final classModel in mockClassesList) {
      final classDoc = firestore.collection('classes').doc(classModel.id);
      await classDoc.set(classModel.toJson());
      debugPrint('Uploaded class: ${classModel.id} - ${classModel.name}');
    }

    // Upload homework data (using correct collection name 'homeworks')
    debugPrint('Uploading ${mockHomeworkList.length} homework items...');
    for (final homework in mockHomeworkList) {
      final homeworkDoc = firestore.collection('homeworks').doc(homework.id);
      await homeworkDoc.set(homework.toJson());
      debugPrint('Uploaded homework: ${homework.id} - ${homework.title}');
    }

    // Upload submissions
    debugPrint('Uploading ${mockHomeworkSubmissions.length} submissions...');
    for (final submission in mockHomeworkSubmissions) {
      final submissionDoc = firestore
          .collection('homework_submissions')
          .doc(submission.id); // Use submission.id instead of composite key
      await submissionDoc.set(submission.toJson());
      debugPrint('Uploaded submission: ${submission.id}');
    }

    debugPrint('Mock data upload completed successfully!');
  } catch (e) {
    debugPrint('Error uploading mock data: $e');
    rethrow;
  }
}
