import '../../../core/models/class_model.dart';

/// Mock data for classes
final List<ClassModel> mockClassesList = [
  ClassModel(
    id: 'class_math_10a',
    name: 'Mathematics 10A',
    subject: 'Mathematics',
    teacherId: 'teacher_001',
    teacherName: 'Dr. <PERSON>',
    studentIds: [
      'student_001',
      'student_002',
      'student_003',
      'student_004',
      'student_005',
      'student_006',
      'student_007',
      'student_008',
      'student_009',
      'student_010',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 180)),
    isActive: true,
  ),

  ClassModel(
    id: 'class_chemistry_11a',
    name: 'Chemistry 11A',
    subject: 'Chemistry',
    teacherId: 'teacher_003',
    teacherName: 'Prof. <PERSON>',
    studentIds: [
      'student_002',
      'student_004',
      'student_006',
      'student_008',
      'student_010',
      'student_011',
      'student_012',
      'student_013',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 150)),
    isActive: true,
  ),

  ClassModel(
    id: 'class_english_12a',
    name: 'English Literature 12A',
    subject: 'English Literature',
    teacherId: 'teacher_004',
    teacherName: 'Ms. Emily <PERSON>',
    studentIds: [
      'student_001',
      'student_003',
      'student_005',
      'student_007',
      'student_009',
      'student_011',
      'student_013',
      'student_014',
      'student_015',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 160)),
    isActive: true,
  ),

  ClassModel(
    id: 'class_cs_12a',
    name: 'Computer Science 12A',
    subject: 'Computer Science',
    teacherId: 'teacher_007',
    teacherName: 'Dr. Alex Thompson',
    studentIds: [
      'student_001',
      'student_002',
      'student_003',
      'student_006',
      'student_008',
      'student_012',
      'student_014',
      'student_016',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 140)),
    isActive: true,
  ),

  ClassModel(
    id: 'class_physics_11b',
    name: 'Physics 11B',
    subject: 'Physics',
    teacherId: 'teacher_002',
    teacherName: 'Dr. Robert Kim',
    studentIds: [
      'student_004',
      'student_005',
      'student_007',
      'student_009',
      'student_010',
      'student_013',
      'student_015',
      'student_017',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 170)),
    isActive: true,
  ),

  ClassModel(
    id: 'class_history_10b',
    name: 'History 10B',
    subject: 'History',
    teacherId: 'teacher_005',
    teacherName: 'Prof. Lisa Wang',
    studentIds: [
      'student_002',
      'student_004',
      'student_007',
      'student_011',
      'student_012',
      'student_016',
      'student_018',
    ],
    createdAt: DateTime.now().subtract(const Duration(days: 155)),
    isActive: true,
  ),
];
