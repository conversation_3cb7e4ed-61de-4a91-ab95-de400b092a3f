import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../controllers/homework_controller.dart';
import '../mock/upload_mock_data.dart';

/// Helper class for homework integration testing and development
class HomeworkIntegrationHelper {
  /// Upload mock data to Firebase for testing
  /// This should only be called in development/debug mode
  static Future<void> uploadMockDataToFirebase() async {
    if (kDebugMode) {
      try {
        await uploadMockHomeworkData();
        debugPrint('✅ Mock homework data uploaded to Firebase successfully');
      } catch (e) {
        debugPrint('❌ Failed to upload mock data: $e');
        rethrow;
      }
    } else {
      debugPrint('⚠️ Mock data upload is only available in debug mode');
    }
  }

  /// Clear all homework-related provider caches
  /// Useful for testing and development
  static void clearAllProviderCaches(WidgetRef ref) {
    HomeworkControllerUtils.invalidateAllHomeworkProviders(ref);
    debugPrint('🔄 All homework provider caches cleared');
  }

  /// Test homework data flow by fetching data for today
  static Future<void> testHomeworkDataFlow(WidgetRef ref) async {
    try {
      debugPrint('🧪 Testing homework data flow...');
      
      // Test fetching homework for today
      final today = DateTime.now();
      final homeworkList = await ref.read(homeworkListProvider(today).future);
      debugPrint('✅ Fetched ${homeworkList.length} homework items for today');
      
      // Test fetching all homework
      final allHomework = await ref.read(allHomeworkProvider.future);
      debugPrint('✅ Fetched ${allHomework.length} total homework items');
      
      // Test fetching a specific homework if available
      if (homeworkList.isNotEmpty) {
        final firstHomework = homeworkList.first;
        final homeworkDetail = await ref.read(homeworkDetailProvider(firstHomework.id).future);
        debugPrint('✅ Fetched homework detail: ${homeworkDetail?.title ?? "Not found"}');
        
        // Test fetching submission for current user
        final currentUserId = ref.read(currentUserIdProvider);
        final submission = await ref.read(
          submissionProvider((homeworkId: firstHomework.id, userId: currentUserId)).future,
        );
        debugPrint('✅ Submission status: ${submission != null ? "Found" : "Not found"}');
      }
      
      debugPrint('🎉 Homework data flow test completed successfully');
    } catch (e) {
      debugPrint('❌ Homework data flow test failed: $e');
      rethrow;
    }
  }

  /// Validate that all required providers are working
  static Future<bool> validateProviders(WidgetRef ref) async {
    try {
      debugPrint('🔍 Validating homework providers...');
      
      // Check repository provider
      final repository = ref.read(homeworkRepositoryProvider);
      debugPrint('✅ Repository provider: ${repository.runtimeType}');
      
      // Check current user provider
      final currentUserId = ref.read(currentUserIdProvider);
      debugPrint('✅ Current user provider: $currentUserId');
      
      // Test basic provider functionality
      final today = DateTime.now();
      final homeworkAsync = ref.read(homeworkListProvider(today));
      
      await homeworkAsync.when(
        loading: () async {
          debugPrint('⏳ Homework list provider is loading...');
        },
        error: (error, stackTrace) async {
          debugPrint('❌ Homework list provider error: $error');
          throw error;
        },
        data: (data) async {
          debugPrint('✅ Homework list provider returned ${data.length} items');
        },
      );
      
      debugPrint('🎉 All providers validated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Provider validation failed: $e');
      return false;
    }
  }

  /// Development helper to log current provider states
  static void logProviderStates(WidgetRef ref) {
    if (kDebugMode) {
      debugPrint('📊 Current Provider States:');
      debugPrint('- Repository: ${ref.read(homeworkRepositoryProvider).runtimeType}');
      debugPrint('- Current User: ${ref.read(currentUserIdProvider)}');
      
      // Note: We can't easily read AsyncValue states without triggering them
      // This is just for basic state logging
      debugPrint('📊 Provider states logged');
    }
  }

  /// Helper to refresh all homework data
  static void refreshAllHomeworkData(WidgetRef ref) {
    HomeworkControllerUtils.invalidateAllHomeworkProviders(ref);
    debugPrint('🔄 All homework data refreshed');
  }

  /// Helper to refresh only homework lists
  static void refreshHomeworkLists(WidgetRef ref) {
    HomeworkControllerUtils.invalidateHomeworkListProviders(ref);
    debugPrint('🔄 Homework lists refreshed');
  }

  /// Helper to refresh only submission data
  static void refreshSubmissionData(WidgetRef ref) {
    HomeworkControllerUtils.invalidateSubmissionProviders(ref);
    debugPrint('🔄 Submission data refreshed');
  }

  /// Helper to refresh only status data
  static void refreshStatusData(WidgetRef ref) {
    HomeworkControllerUtils.invalidateStatusProviders(ref);
    debugPrint('🔄 Status data refreshed');
  }
}

/// Extension methods for easier debugging
extension HomeworkIntegrationDebug on WidgetRef {
  /// Quick method to test homework integration
  Future<void> testHomeworkIntegration() async {
    await HomeworkIntegrationHelper.testHomeworkDataFlow(this);
  }

  /// Quick method to validate providers
  Future<bool> validateHomeworkProviders() async {
    return await HomeworkIntegrationHelper.validateProviders(this);
  }

  /// Quick method to refresh all homework data
  void refreshHomework() {
    HomeworkIntegrationHelper.refreshAllHomeworkData(this);
  }

  /// Quick method to clear all caches
  void clearHomeworkCaches() {
    HomeworkIntegrationHelper.clearAllProviderCaches(this);
  }
}
