import 'package:flutter_test/flutter_test.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../../../core/models/class_model.dart';
import '../models/homework_model.dart';

/// Test suite for class-specific filtering functionality
void main() {
  group('Class Filtering Tests', () {
    late List<HomeworkModel> testHomeworkList;
    late List<ClassModel> testClassList;

    setUp(() {
      // Create test classes
      testClassList = [
        _createTestClass('class_math_001', 'Mathematics 10A', 'Mathematics'),
        _createTestClass('class_science_001', 'Science 10B', 'Science'),
        _createTestClass('class_english_001', 'English 10C', 'English'),
      ];

      // Create test homework for different classes
      testHomeworkList = [
        // Math class homework
        _createTestHomework(
          'hw_math_001',
          'Mathematics',
          'Algebra Practice',
          AssignmentType.classAssignment,
          classId: 'class_math_001',
        ),
        _createTestHomework(
          'hw_math_002',
          'Mathematics',
          'Geometry Problems',
          AssignmentType.classAssignment,
          classId: 'class_math_001',
        ),
        
        // Science class homework
        _createTestHomework(
          'hw_science_001',
          'Science',
          'Chemistry Lab Report',
          AssignmentType.classAssignment,
          classId: 'class_science_001',
        ),
        
        // English class homework
        _createTestHomework(
          'hw_english_001',
          'English',
          'Essay Writing',
          AssignmentType.classAssignment,
          classId: 'class_english_001',
        ),
        
        // Individual assignments (no class)
        _createTestHomework(
          'hw_individual_001',
          'Mathematics',
          'Personal Math Tutoring',
          AssignmentType.individual,
          assignedUserIds: ['student_001'],
        ),
        
        // Group assignments (no class)
        _createTestHomework(
          'hw_group_001',
          'Science',
          'Group Project',
          AssignmentType.group,
          assignedUserIds: ['student_001', 'student_002', 'student_003'],
        ),
      ];
    });

    group('Class Model Tests', () {
      test('should create class model with correct properties', () {
        final classModel = testClassList.first;
        
        expect(classModel.id, 'class_math_001');
        expect(classModel.name, 'Mathematics 10A');
        expect(classModel.subject, 'Mathematics');
        expect(classModel.isActive, isTrue);
        expect(classModel.studentIds, isNotEmpty);
      });

      test('should convert class model to/from JSON correctly', () {
        final originalClass = testClassList.first;
        final json = originalClass.toJson();
        final reconstructedClass = ClassModel.fromJson(json);
        
        expect(reconstructedClass.id, originalClass.id);
        expect(reconstructedClass.name, originalClass.name);
        expect(reconstructedClass.subject, originalClass.subject);
        expect(reconstructedClass.teacherId, originalClass.teacherId);
        expect(reconstructedClass.studentIds, originalClass.studentIds);
        expect(reconstructedClass.isActive, originalClass.isActive);
      });

      test('should handle copyWith correctly', () {
        final originalClass = testClassList.first;
        final updatedClass = originalClass.copyWith(
          name: 'Updated Mathematics 10A',
          isActive: false,
        );
        
        expect(updatedClass.name, 'Updated Mathematics 10A');
        expect(updatedClass.isActive, isFalse);
        expect(updatedClass.id, originalClass.id); // Should remain unchanged
        expect(updatedClass.subject, originalClass.subject); // Should remain unchanged
      });
    });

    group('Class-Based Homework Filtering Tests', () {
      test('should filter homework by specific class correctly', () {
        final mathClassHomework = testHomeworkList
            .where((hw) => hw.classId == 'class_math_001')
            .toList();
        
        expect(mathClassHomework.length, 2);
        expect(mathClassHomework.every((hw) => hw.subject == 'Mathematics'), isTrue);
        expect(mathClassHomework.every((hw) => hw.assignmentType == AssignmentType.classAssignment), isTrue);
      });

      test('should filter homework by different classes', () {
        final scienceClassHomework = testHomeworkList
            .where((hw) => hw.classId == 'class_science_001')
            .toList();
        
        final englishClassHomework = testHomeworkList
            .where((hw) => hw.classId == 'class_english_001')
            .toList();
        
        expect(scienceClassHomework.length, 1);
        expect(englishClassHomework.length, 1);
        expect(scienceClassHomework.first.subject, 'Science');
        expect(englishClassHomework.first.subject, 'English');
      });

      test('should exclude non-class assignments from class filtering', () {
        final classAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
            .toList();
        
        expect(classAssignments.length, 4); // Only class assignments
        expect(classAssignments.every((hw) => hw.classId != null), isTrue);
      });

      test('should handle empty class filter (show all class assignments)', () {
        final allClassAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
            .toList();
        
        expect(allClassAssignments.length, 4);
        
        // Group by class
        final groupedByClass = <String, List<HomeworkModel>>{};
        for (final hw in allClassAssignments) {
          final classId = hw.classId!;
          groupedByClass[classId] = (groupedByClass[classId] ?? [])..add(hw);
        }
        
        expect(groupedByClass.keys.length, 3); // 3 different classes
        expect(groupedByClass['class_math_001']?.length, 2);
        expect(groupedByClass['class_science_001']?.length, 1);
        expect(groupedByClass['class_english_001']?.length, 1);
      });
    });

    group('Combined Assignment Type and Class Filtering Tests', () {
      test('should filter by class assignments and specific class', () {
        final mathClassAssignments = testHomeworkList
            .where((hw) => 
                hw.assignmentType == AssignmentType.classAssignment &&
                hw.classId == 'class_math_001')
            .toList();
        
        expect(mathClassAssignments.length, 2);
        expect(mathClassAssignments.every((hw) => hw.subject == 'Mathematics'), isTrue);
      });

      test('should not include individual/group assignments in class filtering', () {
        final individualAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.individual)
            .toList();
        
        final groupAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.group)
            .toList();
        
        expect(individualAssignments.length, 1);
        expect(groupAssignments.length, 1);
        expect(individualAssignments.first.classId, isNull);
        expect(groupAssignments.first.classId, isNull);
      });

      test('should handle mixed assignment types correctly', () {
        final allHomework = testHomeworkList;
        
        final classCount = allHomework.where((hw) => hw.assignmentType == AssignmentType.classAssignment).length;
        final individualCount = allHomework.where((hw) => hw.assignmentType == AssignmentType.individual).length;
        final groupCount = allHomework.where((hw) => hw.assignmentType == AssignmentType.group).length;
        
        expect(classCount, 4);
        expect(individualCount, 1);
        expect(groupCount, 1);
        expect(classCount + individualCount + groupCount, allHomework.length);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle homework with null class ID', () {
        final homeworkWithNullClass = _createTestHomework(
          'hw_null_class',
          'Test Subject',
          'Test Title',
          AssignmentType.classAssignment,
          classId: null,
        );
        
        expect(homeworkWithNullClass.classId, isNull);
        expect(homeworkWithNullClass.assignmentType, AssignmentType.classAssignment);
      });

      test('should handle empty class list', () {
        final emptyClassList = <ClassModel>[];
        expect(emptyClassList.isEmpty, isTrue);
      });

      test('should handle homework with non-existent class ID', () {
        final homeworkWithInvalidClass = _createTestHomework(
          'hw_invalid_class',
          'Test Subject',
          'Test Title',
          AssignmentType.classAssignment,
          classId: 'non_existent_class',
        );
        
        final matchingClass = testClassList
            .where((cls) => cls.id == homeworkWithInvalidClass.classId)
            .firstOrNull;
        
        expect(matchingClass, isNull);
      });

      test('should handle class filtering with mixed valid and invalid class IDs', () {
        final mixedHomework = [
          ...testHomeworkList,
          _createTestHomework(
            'hw_invalid',
            'Test',
            'Invalid Class Homework',
            AssignmentType.classAssignment,
            classId: 'invalid_class_id',
          ),
        ];
        
        final validClassHomework = mixedHomework
            .where((hw) => 
                hw.assignmentType == AssignmentType.classAssignment &&
                testClassList.any((cls) => cls.id == hw.classId))
            .toList();
        
        expect(validClassHomework.length, 4); // Original 4 valid class assignments
      });
    });
  });
}

/// Helper function to create test class
ClassModel _createTestClass(String id, String name, String subject) {
  return ClassModel(
    id: id,
    name: name,
    subject: subject,
    teacherId: 'teacher_001',
    teacherName: 'Test Teacher',
    studentIds: ['student_001', 'student_002', 'student_003'],
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    isActive: true,
  );
}

/// Helper function to create test homework
HomeworkModel _createTestHomework(
  String id,
  String subject,
  String title,
  AssignmentType assignmentType, {
  String? classId,
  List<String>? assignedUserIds,
}) {
  return HomeworkModel(
    id: id,
    subject: subject,
    title: title,
    description: 'Test homework description',
    assignedAt: DateTime.now().subtract(const Duration(days: 1)),
    dueAt: DateTime.now().add(const Duration(days: 7)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: [],
    classId: classId,
    teacherId: 'teacher_001',
    assignmentType: assignmentType,
    assignedUserIds: assignedUserIds ?? [],
  );
}
